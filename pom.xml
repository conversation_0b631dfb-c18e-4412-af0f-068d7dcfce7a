<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.github.luckygc</groupId>
	<artifactId>pg-queue</artifactId>
	<version>1.1.0</version>
	<name>pg-queue</name>
	<description>pg-queue</description>

	<licenses>
		<license>
			<name>The Apache Software License, Version 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>

	<repositories>
		<repository>
			<id>public</id>
			<name>aliyun nexus</name>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases>
				<enabled>true</enabled>
			</releases>
		</repository>
		<repository>
			<id>jitpack.io</id>
			<url>https://jitpack.io</url>
		</repository>
	</repositories>

	<properties>
		<project.build.encoding>UTF-8</project.build.encoding>
		<java.version>17</java.version>
		<jspecify.version>1.0.0</jspecify.version>
		<junit.version>5.13.3</junit.version>
		<assertj.version>3.27.3</assertj.version>
		<checkstyle.version>10.26.1</checkstyle.version>
		<spotless.version>2.44.5</spotless.version>
        <spring.version>6.2.9</spring.version>
	</properties>

	<dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.7</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.17</version>
        </dependency>

		<dependency>
			<groupId>org.jspecify</groupId>
			<artifactId>jspecify</artifactId>
			<version>${jspecify.version}</version>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
			<version>${assertj.version}</version>
			<scope>test</scope>
		</dependency>

        <!-- Testcontainers 依赖 -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>1.21.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>1.21.3</version>
            <scope>test</scope>
        </dependency>
	</dependencies>

	<profiles>
		<!--开发环境-->
		<profile>
			<id>dev</id>
			<properties>
				<profiles.active>dev</profiles.active>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>3.6.0</version>
				<dependencies>
					<dependency>
						<groupId>com.puppycrawl.tools</groupId>
						<artifactId>checkstyle</artifactId>
						<version>${checkstyle.version}</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>validate</id>
						<phase>validate</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<configLocation>/tools/maven/custom_google_checks.xml</configLocation>
					<suppressionsLocation>/tools/maven/suppressions.xml</suppressionsLocation>
					<includeTestSourceDirectory>true</includeTestSourceDirectory>
					<failOnViolation>true</failOnViolation>
					<outputFile>checkstyle-report.xml</outputFile>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.14.0</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.encoding}</encoding>
					<!--                    <useIncrementalCompilation>false</useIncrementalCompilation>-->
					<compilerArgs>
						<arg>-parameters</arg>
					</compilerArgs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.5.3</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

		</plugins>
	</build>

</project>

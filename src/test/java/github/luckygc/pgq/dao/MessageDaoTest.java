package github.luckygc.pgq.dao;

import github.luckygc.pgq.model.Message;
import github.luckygc.pgq.model.MessageDO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@Testcontainers
class MessageDaoTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    private JdbcTemplate jdbcTemplate;
    private MessageDao messageDao;

    @BeforeEach
    void setUp() throws IOException {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(postgres.getJdbcUrl());
        dataSource.setUsername(postgres.getUsername());
        dataSource.setPassword(postgres.getPassword());
        dataSource.setDriverClassName("org.postgresql.Driver");

        jdbcTemplate = new JdbcTemplate(dataSource);

        // 执行DDL脚本
        String ddlScript = Files.readString(Paths.get("src/main/resources/ddl.sql"));
        jdbcTemplate.execute(ddlScript);

        messageDao = new MessageDao(jdbcTemplate);
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        jdbcTemplate.execute("TRUNCATE TABLE pgmq_pending_queue, pgmq_processing_queue, pgmq_invisible_queue, pgmq_dead_queue");
    }

    @Test
    void shouldInsertMessageIntoPending() {
        MessageDO messageDO = MessageDO.Builder.create()
                .topic("test-topic")
                .priority(5)
                .payload("test payload")
                .attempt(0)
                .build();

        messageDao.insertIntoPending(messageDO);

        Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_pending_queue WHERE topic = ?",
                Integer.class,
                "test-topic"
        );
        assertThat(count).isEqualTo(1);
    }

    @Test
    void shouldInsertMultipleMessagesIntoPending() {
        List<MessageDO> messageDOs = Arrays.asList(
                MessageDO.Builder.create()
                        .topic("test-topic")
                        .priority(1)
                        .payload("message1")
                        .attempt(0)
                        .build(),
                MessageDO.Builder.create()
                        .topic("test-topic")
                        .priority(2)
                        .payload("message2")
                        .attempt(0)
                        .build()
        );

        messageDao.insertIntoPending(messageDOs);

        Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_pending_queue WHERE topic = ?",
                Integer.class,
                "test-topic"
        );
        assertThat(count).isEqualTo(2);
    }

    @Test
    void shouldInsertMessageIntoInvisible() {
        MessageDO messageDO = MessageDO.Builder.create()
                .topic("test-topic")
                .priority(5)
                .payload("test payload")
                .attempt(0)
                .build();

        LocalDateTime visibleTime = LocalDateTime.now().plusMinutes(5);
        messageDao.insertIntoInvisible(messageDO, visibleTime);

        Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_invisible_queue WHERE topic = ?",
                Integer.class,
                "test-topic"
        );
        assertThat(count).isEqualTo(1);
    }

    @Test
    void shouldGetPendingMessagesAndMoveToProcessing() {
        // 先插入一些待处理消息
        MessageDO messageDO1 = MessageDO.Builder.create()
                .topic("test-topic")
                .priority(10)
                .payload("high priority")
                .attempt(0)
                .build();

        MessageDO messageDO2 = MessageDO.Builder.create()
                .topic("test-topic")
                .priority(5)
                .payload("low priority")
                .attempt(0)
                .build();

        messageDao.insertIntoPending(Arrays.asList(messageDO1, messageDO2));

        // 获取消息并移动到处理队列
        LocalDateTime timeoutTime = LocalDateTime.now().plusMinutes(30);
        List<Message> messages = messageDao.getPendingMessagesAndMoveToProcessing("test-topic", 10, timeoutTime);

        assertThat(messages).hasSize(2);
        // 应该按优先级排序，高优先级在前
        assertThat(messages.get(0).getPayload()).isEqualTo("high priority");
        assertThat(messages.get(0).getPriority()).isEqualTo(10);
        assertThat(messages.get(1).getPayload()).isEqualTo("low priority");
        assertThat(messages.get(1).getPriority()).isEqualTo(5);

        // 验证消息已从待处理队列移除
        Integer pendingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_pending_queue WHERE topic = ?",
                Integer.class,
                "test-topic"
        );
        assertThat(pendingCount).isEqualTo(0);

        // 验证消息已移动到处理队列
        Integer processingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_processing_queue WHERE topic = ?",
                Integer.class,
                "test-topic"
        );
        assertThat(processingCount).isEqualTo(2);
    }

    @Test
    void shouldLimitNumberOfMessagesRetrieved() {
        // 插入5条消息
        for (int i = 0; i < 5; i++) {
            MessageDO messageDO = MessageDO.Builder.create()
                    .topic("test-topic")
                    .priority(i)
                    .payload("message" + i)
                    .attempt(0)
                    .build();
            messageDao.insertIntoPending(messageDO);
        }

        // 只获取3条消息
        LocalDateTime timeoutTime = LocalDateTime.now().plusMinutes(30);
        List<Message> messages = messageDao.getPendingMessagesAndMoveToProcessing("test-topic", 3, timeoutTime);

        assertThat(messages).hasSize(3);

        // 验证还有2条消息在待处理队列
        Integer pendingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_pending_queue WHERE topic = ?",
                Integer.class,
                "test-topic"
        );
        assertThat(pendingCount).isEqualTo(2);
    }

    @Test
    void shouldDeleteProcessingMessageById() {
        // 先插入一条消息到处理队列
        jdbcTemplate.update(
                "INSERT INTO pgmq_processing_queue (id, create_time, topic, priority, payload, attempt, timeout_time) VALUES (?, ?, ?, ?, ?, ?, ?)",
                1L, LocalDateTime.now(), "test-topic", 0, "test payload", 0, LocalDateTime.now().plusMinutes(30)
        );

        int deleteCount = messageDao.deleteProcessingMessageById(1L);

        assertThat(deleteCount).isEqualTo(1);

        Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_processing_queue WHERE id = ?",
                Integer.class,
                1L
        );
        assertThat(count).isEqualTo(0);
    }

    @Test
    void shouldMoveProcessingMessageToDeadById() {
        // 先插入一条消息到处理队列
        jdbcTemplate.update(
                "INSERT INTO pgmq_processing_queue (id, create_time, topic, priority, payload, attempt, timeout_time) VALUES (?, ?, ?, ?, ?, ?, ?)",
                1L, LocalDateTime.now(), "test-topic", 0, "test payload", 0, LocalDateTime.now().plusMinutes(30)
        );

        int moveCount = messageDao.moveProcessingMessageToDeadById(1L);

        assertThat(moveCount).isEqualTo(1);

        // 验证消息已从处理队列移除
        Integer processingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_processing_queue WHERE id = ?",
                Integer.class,
                1L
        );
        assertThat(processingCount).isEqualTo(0);

        // 验证消息已移动到死信队列
        Integer deadCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_dead_queue WHERE id = ?",
                Integer.class,
                1L
        );
        assertThat(deadCount).isEqualTo(1);
    }

    @Test
    void shouldMoveProcessingMessageToPendingById() {
        // 先插入一条消息到处理队列
        jdbcTemplate.update(
                "INSERT INTO pgmq_processing_queue (id, create_time, topic, priority, payload, attempt, timeout_time) VALUES (?, ?, ?, ?, ?, ?, ?)",
                1L, LocalDateTime.now(), "test-topic", 0, "test payload", 0, LocalDateTime.now().plusMinutes(30)
        );

        int moveCount = messageDao.moveProcessingMessageToPendingById(1L);

        assertThat(moveCount).isEqualTo(1);

        // 验证消息已从处理队列移除
        Integer processingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_processing_queue WHERE id = ?",
                Integer.class,
                1L
        );
        assertThat(processingCount).isEqualTo(0);

        // 验证消息已移动到待处理队列
        Integer pendingCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM pgmq_pending_queue WHERE id = ?",
                Integer.class,
                1L
        );
        assertThat(pendingCount).isEqualTo(1);
    }

    @Test
    void shouldReturnZeroWhenDeletingNonExistentMessage() {
        int deleteCount = messageDao.deleteProcessingMessageById(999L);
        assertThat(deleteCount).isEqualTo(0);
    }

    @Test
    void shouldReturnZeroWhenMovingNonExistentMessageToDead() {
        int moveCount = messageDao.moveProcessingMessageToDeadById(999L);
        assertThat(moveCount).isEqualTo(0);
    }

    @Test
    void shouldReturnZeroWhenMovingNonExistentMessageToPending() {
        int moveCount = messageDao.moveProcessingMessageToPendingById(999L);
        assertThat(moveCount).isEqualTo(0);
    }
}
